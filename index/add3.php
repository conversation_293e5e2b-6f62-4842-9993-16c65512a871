<?php
$title = '批量交单';
require_once('head.php');
$addsalt = md5(mt_rand(0, 999) . time());
$_SESSION['addsalt'] = $addsalt;
?>
<link rel="stylesheet" href="../assets/css/mobile-select-optimization.css" type="text/css" />
<link rel="stylesheet" href="../assets/css/select-optimization.css" type="text/css" />
<link rel="stylesheet" href="../assets/css/scrollbar-fix.css" type="text/css" />

<div class="wrapper-md control" id="add" v-cloak>
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default" id="addorder" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">无查提交</div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
                    <form class="form-horizontal devform">
                        <div class="form-group">
                            <label class="col-sm-1 control-label">选择项目:</label>
                            <div class="col-sm-9">
<el-select id="select" v-model="cid" @change="tips(cid)" filterable placeholder="直接输入名称即可搜索" style="width:100%">
    <el-option 
        v-for="class2 in class1" 
        :key="class2.cid" 
        :label="class2.price + '积分-' + class2.name"
        :value="class2.cid"
    >
        <div class="el-option-content">
            <span class="project-price">{{ class2.price }}积分</span>
            <span class="project-name">{{ class2.name }}</span>
        </div>
    </el-option>
</el-select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-1 control-label">账号信息:</label>
                            <div class="col-sm-9">
                                <textarea rows="12" cols="80" class="layui-textarea" v-model="userinfo" placeholder="填写格式：&#10例： 手机号 密码 课程 &#10或： 学校 账号 密码 课程 &#10多账号下单必须换行！ &#10务必一行一条信息且账号密码正确！" style="border-radius: 8px;"></textarea>
                                <div style="height:5px"></div>
                                <span class="help-block m-b-none" style="color:red;" id="warning">
                                    <span v-html="content"></span>
                                </span>
                            </div>
                        </div>
                        <div class="col-sm-offset-1">
                            <button type="button" @click="showExcelModal" class="layui-btn layui-btn-warm">上传Excel</button>
                            <input type="button" @click="checkUserinfo" value="🔧 检查数据" class="layui-btn layui-btn-danger" title="智能校正：自动修复多余空格、密码中的空格、中文符号等问题" />
                            <input type="button" @click="add" value="立即提交" class="layui-btn layui-btn-normal" />
                            <input type="reset" value="重置" class="layui-btn layui-btn-primary" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="wrapper-md control" id="loglist" style="margin-top:-35px">
            <div class="panel panel-default" id="addorder" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">提交结果</div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>平台 学校 账号 密码 课程名称</th>
                            <th>预计扣费</th>
                            <th>余额</th>
                            <th>操作时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="res in row.data">
                            <td>{{res.id}}</td>
                            <td>{{res.text}}</td>
                            <td>{{res.money}}</td>
                            <td>{{res.smoney}}</td>
                            <td>{{res.addtime}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <ul class="pagination" v-if="row.last_page > 1">
                <!--by 青卡 Vue分页 -->
                <li class="disabled"><a @click="get(1)">首页</a></li>
                <li class="disabled"><a @click="row.current_page > 1 ? get(row.current_page - 1) : ''">&laquo;</a></li>
                <li @click="get(row.current_page - 3)" v-if="row.current_page - 3 >= 1"><a>{{ row.current_page - 3 }}</a></li>
                <li @click="get(row.current_page - 2)" v-if="row.current_page - 2 >= 1"><a>{{ row.current_page - 2 }}</a></li>
                <li @click="get(row.current_page - 1)" v-if="row.current_page - 1 >= 1"><a>{{ row.current_page - 1 }}</a></li>
                <li :class="{'active': row.current_page == row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
                <li @click="get(row.current_page + 1)" v-if="row.current_page + 1 <= row.last_page"><a>{{ row.current_page + 1 }}</a></li>
                <li @click="get(row.current_page + 2)" v-if="row.current_page + 2 <= row.last_page"><a>{{ row.current_page + 2 }}</a></li>
                <li @click="get(row.current_page + 3)" v-if="row.current_page + 3 <= row.last_page"><a>{{ row.current_page + 3 }}</a></li>
                <li class="disabled"><a @click="row.last_page > row.current_page ? get(row.current_page + 1) : ''">&raquo;</a></li>
                <li class="disabled"><a @click="get(row.last_page)">尾页</a></li>
            </ul>
        </div>
    </div>
</div>

<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>
<script src="../assets/js/mobile-select-optimization.js"></script>
<script src="../assets/js/xlsx.full.min.js"></script>

<script>
// 确保Element UI已加载
if (typeof ELEMENT !== 'undefined') {
    Vue.use(ELEMENT);
}

var vm = new Vue({
    el: "#add",
    data: {
        row: [],
        check_row: [],
        userinfo: '',
        cid: '',
        class1: [],
        class3: [],
        activems: false,
        content: '',
        excelData: []
    },
    methods: {
        add: function () {
            if (!this.cid) {
                layer.msg("请先选择平台");
                return false;
            }
            if (!this.userinfo) {
                layer.msg("请填写信息");
                return false;
            }
            var userinfo = this.userinfo.replace(/\r\n/g, "[br]").replace(/\n/g, "[br]").replace(/\r/g, "[br]");
            userinfo = userinfo.split('[br]'); // 分割

            for (var i = 0; this.class1.length > i; i++) {
                if (this.class1[i].cid == this.cid) {
                    var price = this.class1[i].price;
                    break;
                }
            }

            var kofei = price * userinfo.length;
            var num = userinfo.length;

            layer.confirm("检测到有<b style='color:red'>" + num + "</b>条账号信息，预计扣费<b style='color:red'>" + kofei + "积分</b>，具体扣费以提交结果为准", {
                title: '温馨提示',
                icon: 3,
                btn: ['确定交单', '取消']
            }, function () {
                var loading = layer.load(2);
                vm.$http.post("/apisub.php?act=add_pl", {
                    cid: vm.cid,
                    userinfo: userinfo,
                    num: num
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(loading);
                    if (data.data.code == 1) {
                        layer.alert(data.data.msg, {
                            icon: 1,
                            title: "提交结果"
                        }, function () {
                            window.location.href = "";
                        });
                    } else {
                        layer.alert(data.data.msg, {
                            icon: 2,
                            title: "提交结果"
                        });
                    }
                }, function () {
                    layer.close(loading);
                    layer.alert("服务器错误");
                });
            });
        },
        getclass: function () {
            var load = layer.load(2);
            this.$http.post("/apisub.php?act=getclassfl").then(function (data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.class1 = data.body.data;
                } else {
                    layer.msg(data.data.msg, {
                        icon: 2
                    });
                }
            });
        },
        showExcelModal: function () {
            layer.open({
                type: 1,
                title: '上传Excel',
                content: 
                    `<input type="file" id="excelFile" accept=".xlsx,.xls" />` +
                    `<button type="button" id="readExcelBtn" onclick="vm.readExcel()">读取并解析文件</button>`+
                    `<br>文件上传后点读取并解析文件<br>所有账号信息会显示在上方的框中<br>读取时会默认丢弃第一行的数据（表头）<br>默认表格格式为 学校 账号 密码 课程`,
                area: ['300px', '200px']
            });
        },
        readExcel: function () {
            var file = document.getElementById('excelFile').files[0];
            if (!file) {
                layer.msg("请选择Excel文件");
                return;
            }
            var reader = new FileReader();
            reader.onload = function (e) {
                var data = e.target.result;
                var workbook = XLSX.read(data, { type: 'binary' });
                var sheetName = workbook.SheetNames[0];
                var sheet = workbook.Sheets[sheetName];
                var json = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '' });
                var userData = json.slice(1).map(row => row.join(' '));
                vm.userinfo = userData.join('\n');
                layer.msg("数据读取成功");
                layer.closeAll();
            };
            reader.readAsBinaryString(file);
        },
        // 智能数据校正功能
        smartCorrectUserinfo: function(text) {
            // 简化的字符替换，避免复杂的Unicode问题
            var corrected = text;

            // 替换常见的中文符号
            corrected = corrected.replace(/，/g, ',');
            corrected = corrected.replace(/。/g, '.');
            corrected = corrected.replace(/；/g, ';');
            corrected = corrected.replace(/：/g, ':');
            corrected = corrected.replace(/？/g, '?');
            corrected = corrected.replace(/！/g, '!');
            corrected = corrected.replace(/"/g, '"');
            corrected = corrected.replace(/"/g, '"');
            corrected = corrected.replace(/'/g, "'");
            corrected = corrected.replace(/'/g, "'");
            corrected = corrected.replace(/（/g, '(');
            corrected = corrected.replace(/）/g, ')');
            corrected = corrected.replace(/【/g, '[');
            corrected = corrected.replace(/】/g, ']');
            corrected = corrected.replace(/｛/g, '{');
            corrected = corrected.replace(/｝/g, '}');
            corrected = corrected.replace(/《/g, '<');
            corrected = corrected.replace(/》/g, '>');
            corrected = corrected.replace(/～/g, '~');
            corrected = corrected.replace(/｀/g, '`');
            corrected = corrected.replace(/＠/g, '@');
            corrected = corrected.replace(/＃/g, '#');
            corrected = corrected.replace(/￥/g, '$');
            corrected = corrected.replace(/％/g, '%');
            corrected = corrected.replace(/＾/g, '^');
            corrected = corrected.replace(/＆/g, '&');
            corrected = corrected.replace(/＊/g, '*');
            corrected = corrected.replace(/－/g, '-');
            corrected = corrected.replace(/＿/g, '_');
            corrected = corrected.replace(/＋/g, '+');
            corrected = corrected.replace(/＝/g, '=');
            corrected = corrected.replace(/｜/g, '|');
            corrected = corrected.replace(/＼/g, '\\');
            corrected = corrected.replace(/／/g, '/');
            corrected = corrected.replace(/　/g, ' ');

            return corrected;

            // 全角数字和字母到半角的映射
            var fullWidthMap = {};
            // 全角数字 0-9
            for (var i = 0; i <= 9; i++) {
                fullWidthMap[String.fromCharCode(0xFF10 + i)] = i.toString();
            }
            // 全角大写字母 A-Z
            for (var i = 0; i < 26; i++) {
                fullWidthMap[String.fromCharCode(0xFF21 + i)] = String.fromCharCode(0x41 + i);
            }
            // 全角小写字母 a-z
            for (var i = 0; i < 26; i++) {
                fullWidthMap[String.fromCharCode(0xFF41 + i)] = String.fromCharCode(0x61 + i);
            }

            // 合并映射表
            var allMap = Object.assign({}, symbolMap, fullWidthMap);

            // 执行字符替换
            var corrected = text;
            for (var key in allMap) {
                corrected = corrected.replace(new RegExp(key, 'g'), allMap[key]);
            }

            return corrected;
        },

        checkUserinfo: function () {
            var lines = this.userinfo.split('\n');
            var errors = [];
            var warnings = [];
            var correctedLines = [];
            var hasCorrections = false;

            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line === '') continue; // 跳过空行

                var originalLine = line;

                // 1. 智能符号校正
                line = this.smartCorrectUserinfo(line);

                // 2. 将Tab键转换为空格
                line = line.replace(/\t/g, ' ');

                // 3. 将多个空格替换为单个空格
                line = line.replace(/\s+/g, ' ');

                // 4. 智能密码校正 - 检测并修复密码中的空格
                var parts = line.split(' ');
                if (parts.length >= 3) {
                    // 对于add3.php，格式是：学校 账号 密码 课程名
                    // 需要特别处理密码部分，确保密码不被空格分割
                    var correctedParts = [];
                    var passwordStartIndex = -1;
                    var courseStartIndex = -1;

                    // 第一部分：学校
                    correctedParts.push(parts[0]);

                    // 第二部分：账号
                    correctedParts.push(parts[1]);

                    // 从第三部分开始可能是密码和课程名
                    // 需要智能判断哪里是密码结束，哪里是课程名开始
                    var remainingParts = parts.slice(2);
                    var password = '';
                    var courseName = '';

                    // 简单策略：假设密码不会包含中文，课程名可能包含中文
                    var passwordParts = [];
                    var courseNameParts = [];
                    var foundCourse = false;

                    for (var j = 0; j < remainingParts.length; j++) {
                        var part = remainingParts[j];
                        // 如果包含中文，很可能是课程名的开始
                        if (/[\u4e00-\u9fa5]/.test(part) && !foundCourse) {
                            foundCourse = true;
                            courseNameParts.push(part);
                        } else if (foundCourse) {
                            courseNameParts.push(part);
                        } else {
                            passwordParts.push(part);
                        }
                    }

                    // 如果没有找到中文（课程名），则最后一个部分作为课程名，其余作为密码
                    if (!foundCourse && remainingParts.length > 1) {
                        courseNameParts = [remainingParts[remainingParts.length - 1]];
                        passwordParts = remainingParts.slice(0, -1);
                    } else if (!foundCourse) {
                        // 只有一个部分，当作密码
                        passwordParts = remainingParts;
                    }

                    // 合并密码部分（不加空格）
                    password = passwordParts.join('');
                    // 合并课程名部分（保留空格）
                    courseName = courseNameParts.join(' ');

                    if (password) correctedParts.push(password);
                    if (courseName) correctedParts.push(courseName);

                    // 重新组合
                    if (correctedParts.length >= 3) {
                        line = correctedParts.join(' ');
                    }
                }

                // 检查是否有修正
                if (originalLine !== line) {
                    hasCorrections = true;
                    warnings.push('第' + (i+1) + '行已自动校正：\n原始：' + originalLine + '\n校正：' + line);
                }

                correctedLines.push(line);

                // 验证最终格式
                var finalParts = line.split(' ');
                if (finalParts.length < 3 || finalParts.length > 4) {
                    errors.push('第' + (i+1) + '行数据数量不正确，此行有' + finalParts.length + '个数据。');
                }

                // 检查密码格式（基本验证）
                if (finalParts.length >= 3) {
                    var password = finalParts[2]; // 第三个是密码
                    if (password.length < 3) {
                        warnings.push('第' + (i+1) + '行密码可能过短：' + password);
                    }
                    // 检查密码是否包含明显的空格（可能校正失败）
                    if (password.includes(' ')) {
                        errors.push('第' + (i+1) + '行密码仍包含空格，请手动检查：' + password);
                    }
                }
            }

            // 更新数据
            this.userinfo = correctedLines.join('\n');

            // 显示结果
            var message = '';
            if (hasCorrections) {
                message += '✅ 已自动校正 ' + warnings.length + ' 行数据\n\n';
                if (warnings.length <= 5) {
                    message += warnings.join('\n\n');
                } else {
                    message += warnings.slice(0, 3).join('\n\n') + '\n\n... 还有 ' + (warnings.length - 3) + ' 行校正';
                }
                message += '\n\n';
            }

            if (errors.length > 0) {
                message += '❌ 发现 ' + errors.length + ' 个错误：\n' + errors.join('\n');
                layer.alert(message, {title: '数据检查结果', icon: 5, area: ['500px', '400px']});
            } else {
                if (hasCorrections) {
                    message += '✅ 所有数据格式正确！';
                    layer.alert(message, {title: '数据校正完成', icon: 1, area: ['500px', '400px']});
                } else {
                    layer.msg('✅ 数据检查通过，无需校正！', {icon: 1});
                }
            }
        },
        tips: function (message) {
            // 确保class1是数组
            if (!Array.isArray(this.class1)) {
                return;
            }

            for (var i = 0; i < this.class1.length; i++) {
                if (this.class1[i].cid == message) {
                    this.show = true;
                    this.content = this.class1[i].content;
                    return false;
                }
            }
        }
    },
    mounted() {
        this.getclass();
    }
});
</script>
<script>
new Vue({
    el: "#loglist",
    data: {
        row: null,
        type: '',
        types: '',
        qq: ''
    },
    methods: {
        get: function (page, a) {
            var load = layer.load(2);
            data = {
                page: page,
                type: this.type,
                types: this.types,
                qq: this.qq
            }
            this.$http.post("/apisub.php?act=loglist1", data, {
                emulateJSON: true
            }).then(function (data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.row = data.body;
                } else {
                    layer.msg(data.data.msg, {
                        icon: 2
                    });
                }
            });
        }
    },
    mounted() {
        this.get(1);
    }
});
</script>
<script src="../assets/js/select-optimization.js"></script>
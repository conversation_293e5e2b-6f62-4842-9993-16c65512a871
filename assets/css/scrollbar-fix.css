/**
 * 滚动条修复样式
 * 专门解决项目名称滚动条不显示的问题
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

/* ========== 强制显示滚动条的通用样式 ========== */

/* 项目名称容器 - 强制滚动条显示 */
.el-option .project-name,
.project-name {
    /* 基础布局 */
    display: block !important;
    width: 100% !important;
    max-width: 280px !important;
    min-height: 30px !important;
    
    /* 文本处理 */
    white-space: nowrap !important;
    overflow-x: scroll !important;
    overflow-y: hidden !important;
    
    /* 内边距确保滚动条可见 */
    padding: 4px 0 12px 0 !important;
    margin: 0 !important;
    
    /* 字体样式 */
    font-size: 14px !important;
    line-height: 1.4 !important;
    color: #333 !important;
    
    /* 确保滚动条始终显示 */
    scrollbar-width: thin !important;
    scrollbar-color: #888 #f1f1f1 !important;
}

/* ========== Webkit 滚动条样式 ========== */
.el-option .project-name::-webkit-scrollbar,
.project-name::-webkit-scrollbar {
    height: 10px !important;
    background-color: #f1f1f1 !important;
    border-radius: 5px !important;
}

.el-option .project-name::-webkit-scrollbar-track,
.project-name::-webkit-scrollbar-track {
    background-color: #f1f1f1 !important;
    border-radius: 5px !important;
    border: 1px solid #e0e0e0 !important;
}

.el-option .project-name::-webkit-scrollbar-thumb,
.project-name::-webkit-scrollbar-thumb {
    background-color: #888 !important;
    border-radius: 5px !important;
    border: 1px solid #f1f1f1 !important;
    min-width: 30px !important;
}

.el-option .project-name::-webkit-scrollbar-thumb:hover,
.project-name::-webkit-scrollbar-thumb:hover {
    background-color: #555 !important;
}

.el-option .project-name::-webkit-scrollbar-thumb:active,
.project-name::-webkit-scrollbar-thumb:active {
    background-color: #333 !important;
}

/* ========== 移动端优化 ========== */
@media only screen and (max-width: 768px) {
    .el-option .project-name,
    .project-name {
        max-width: 250px !important;
        min-height: 35px !important;
        padding: 6px 0 14px 0 !important;
        font-size: 15px !important;
    }
    
    .el-option .project-name::-webkit-scrollbar,
    .project-name::-webkit-scrollbar {
        height: 12px !important;
    }
    
    .el-option .project-name::-webkit-scrollbar-thumb,
    .project-name::-webkit-scrollbar-thumb {
        min-width: 40px !important;
    }
}

/* ========== 超小屏幕优化 ========== */
@media only screen and (max-width: 375px) {
    .el-option .project-name,
    .project-name {
        max-width: 200px !important;
        font-size: 14px !important;
    }
}

/* ========== 确保在所有容器中都生效 ========== */
.el-select-dropdown .el-option .project-name,
.el-option-content .project-name,
div[class*="project-name"],
span[class*="project-name"] {
    /* 继承上面的所有样式 */
    display: block !important;
    white-space: nowrap !important;
    overflow-x: scroll !important;
    overflow-y: hidden !important;
    max-width: 280px !important;
    min-height: 30px !important;
    padding: 4px 0 12px 0 !important;
    scrollbar-width: thin !important;
    scrollbar-color: #888 #f1f1f1 !important;
}

/* ========== 调试样式（可选） ========== */
.debug-scrollbar .project-name {
    border: 1px dashed #ff0000 !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

/* ========== 强制重绘触发器 ========== */
.el-option:hover .project-name,
.project-name:hover {
    transform: translateZ(0) !important;
}

/* ========== 兼容性增强 ========== */
.el-option .project-name {
    /* 确保在flex容器中正确显示 */
    flex-shrink: 1 !important;
    min-width: 0 !important;
}

/* ========== 滚动条可见性强制 ========== */
.el-option .project-name::-webkit-scrollbar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.el-option .project-name::-webkit-scrollbar-track {
    display: block !important;
    visibility: visible !important;
}

.el-option .project-name::-webkit-scrollbar-thumb {
    display: block !important;
    visibility: visible !important;
}

/* ========== 文本内容确保 ========== */
.el-option .project-name {
    /* 确保有足够的内容来触发滚动 */
    min-width: 100px !important;
}

/* 为短文本添加最小宽度 */
.el-option .project-name:empty:before {
    content: "项目名称" !important;
    color: #999 !important;
}

/* ========== 最终保险样式 ========== */
* .project-name {
    overflow-x: scroll !important;
    scrollbar-width: thin !important;
}

*::-webkit-scrollbar {
    height: 10px !important;
}

/* ========== 打印时隐藏滚动条 ========== */
@media print {
    .el-option .project-name::-webkit-scrollbar {
        display: none !important;
    }
    
    .el-option .project-name {
        overflow: visible !important;
        white-space: normal !important;
    }
}

/**
 * 下拉选择框优化脚本
 * 处理长文本检测、滑动效果和移动端适配
 * 作者: AI Assistant
 * 创建时间: 2025-08-25
 */

(function() {
    'use strict';

    // 检测是否为移动设备
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
               window.innerWidth <= 768;
    }

    // 初始化选择框优化
    function initSelectOptimization() {
        // 等待Vue和Element UI加载完成
        if (typeof Vue === 'undefined' || typeof ELEMENT === 'undefined') {
            setTimeout(initSelectOptimization, 100);
            return;
        }

        // 监听下拉框打开事件
        document.addEventListener('click', function(e) {
            const selectElement = e.target.closest('.el-select');
            if (selectElement) {
                setTimeout(function() {
                    optimizeDropdownOptions();
                }, 50);
            }
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            setTimeout(function() {
                optimizeDropdownOptions();
            }, 100);
        });
    }

    // 优化下拉框选项
    function optimizeDropdownOptions() {
        const dropdowns = document.querySelectorAll('.el-select-dropdown:not(.el-select-dropdown--hidden)');
        
        dropdowns.forEach(function(dropdown) {
            const options = dropdown.querySelectorAll('.el-option');
            
            options.forEach(function(option) {
                // 检查是否已经优化过
                if (option.classList.contains('select-optimized')) {
                    return;
                }

                // 添加优化标记
                option.classList.add('select-optimized');

                // 优化选项内容
                optimizeOptionContent(option);
            });

            // 调整下拉框位置和大小
            adjustDropdownPosition(dropdown);
        });
    }

    // 优化选项内容
    function optimizeOptionContent(option) {
        const projectName = option.querySelector('.project-name');
        const projectPrice = option.querySelector('.project-price');
        
        if (!projectName) return;

        const nameText = projectName.textContent || projectName.innerText;
        
        // 检测长文本
        if (nameText.length > 30) {
            projectName.classList.add('long-text');
        }

        // 为所有设备设置手动滑动功能
        setupManualScroll(option, projectName);
    }

    // 设置手动滑动功能
    function setupManualScroll(option, projectName) {
        const nameText = projectName.textContent || projectName.innerText;

        // 确保项目名称支持手动滑动
        projectName.style.whiteSpace = 'nowrap';
        projectName.style.overflowX = 'auto';
        projectName.style.overflowY = 'hidden';

        // 添加滑动提示（仅在文本溢出时）
        function checkOverflow() {
            if (projectName.scrollWidth > projectName.clientWidth) {
                projectName.title = '可以左右滑动查看完整内容：' + nameText;
                projectName.style.cursor = 'grab';
            } else {
                projectName.title = nameText;
                projectName.style.cursor = 'default';
            }
        }

        // 初始检查
        setTimeout(checkOverflow, 100);

        // 窗口大小变化时重新检查
        window.addEventListener('resize', checkOverflow);

        // 添加拖拽滑动支持（移动端友好）
        let isScrolling = false;
        let startX = 0;
        let scrollLeft = 0;

        projectName.addEventListener('mousedown', function(e) {
            if (projectName.scrollWidth <= projectName.clientWidth) return;

            isScrolling = true;
            startX = e.pageX - projectName.offsetLeft;
            scrollLeft = projectName.scrollLeft;
            projectName.style.cursor = 'grabbing';
            e.preventDefault();
        });

        projectName.addEventListener('mousemove', function(e) {
            if (!isScrolling) return;

            const x = e.pageX - projectName.offsetLeft;
            const walk = (x - startX) * 2;
            projectName.scrollLeft = scrollLeft - walk;
            e.preventDefault();
        });

        projectName.addEventListener('mouseup', function() {
            isScrolling = false;
            projectName.style.cursor = 'grab';
        });

        projectName.addEventListener('mouseleave', function() {
            isScrolling = false;
            if (projectName.scrollWidth > projectName.clientWidth) {
                projectName.style.cursor = 'grab';
            }
        });

        // 触摸设备支持
        projectName.addEventListener('touchstart', function(e) {
            if (projectName.scrollWidth <= projectName.clientWidth) return;

            startX = e.touches[0].pageX - projectName.offsetLeft;
            scrollLeft = projectName.scrollLeft;
        });

        projectName.addEventListener('touchmove', function(e) {
            if (projectName.scrollWidth <= projectName.clientWidth) return;

            const x = e.touches[0].pageX - projectName.offsetLeft;
            const walk = (x - startX) * 2;
            projectName.scrollLeft = scrollLeft - walk;
            e.preventDefault();
        });
    }

    // 调整下拉框位置
    function adjustDropdownPosition(dropdown) {
        const rect = dropdown.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 移动端特殊处理
        if (isMobileDevice()) {
            // 确保下拉框不超出屏幕边界
            if (rect.right > viewportWidth) {
                const offset = rect.right - viewportWidth + 20;
                const currentLeft = parseInt(dropdown.style.left) || 0;
                dropdown.style.left = (currentLeft - offset) + 'px';
            }

            if (rect.left < 0) {
                dropdown.style.left = '10px';
            }

            // 限制高度
            if (rect.height > viewportHeight * 0.6) {
                dropdown.style.maxHeight = (viewportHeight * 0.6) + 'px';
                dropdown.style.overflowY = 'auto';
            }
        }
    }

    // 处理搜索高亮
    function highlightSearchText(text, searchTerm) {
        if (!searchTerm) return text;
        
        const regex = new RegExp('(' + searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }

    // 监听Element UI的select组件事件
    function setupElementUIListeners() {
        // 监听搜索事件
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('el-input__inner') && 
                e.target.closest('.el-select')) {
                
                const searchTerm = e.target.value;
                setTimeout(function() {
                    highlightSearchResults(searchTerm);
                }, 50);
            }
        });
    }

    // 高亮搜索结果
    function highlightSearchResults(searchTerm) {
        const dropdowns = document.querySelectorAll('.el-select-dropdown:not(.el-select-dropdown--hidden)');
        
        dropdowns.forEach(function(dropdown) {
            const options = dropdown.querySelectorAll('.el-option .project-name');
            
            options.forEach(function(nameElement) {
                const originalText = nameElement.getAttribute('data-original-text') || nameElement.textContent;
                
                if (!nameElement.getAttribute('data-original-text')) {
                    nameElement.setAttribute('data-original-text', originalText);
                }
                
                if (searchTerm) {
                    nameElement.innerHTML = highlightSearchText(originalText, searchTerm);
                } else {
                    nameElement.innerHTML = originalText;
                }
            });
        });
    }

    // 添加键盘导航支持
    function setupKeyboardNavigation() {
        document.addEventListener('keydown', function(e) {
            const activeDropdown = document.querySelector('.el-select-dropdown:not(.el-select-dropdown--hidden)');
            if (!activeDropdown) return;

            const options = activeDropdown.querySelectorAll('.el-option:not(.is-disabled)');
            const currentActive = activeDropdown.querySelector('.el-option.hover');
            
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                e.preventDefault();
                
                let nextIndex = 0;
                if (currentActive) {
                    const currentIndex = Array.from(options).indexOf(currentActive);
                    nextIndex = e.key === 'ArrowDown' ? 
                        (currentIndex + 1) % options.length : 
                        (currentIndex - 1 + options.length) % options.length;
                }
                
                // 移除当前高亮
                options.forEach(opt => opt.classList.remove('hover'));
                
                // 添加新高亮
                if (options[nextIndex]) {
                    options[nextIndex].classList.add('hover');
                    options[nextIndex].scrollIntoView({ block: 'nearest' });
                }
            }
        });
    }

    // 性能优化：防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 初始化所有功能
    function init() {
        initSelectOptimization();
        setupElementUIListeners();
        setupKeyboardNavigation();
        
        // 添加防抖的resize监听器
        const debouncedOptimize = debounce(optimizeDropdownOptions, 150);
        window.addEventListener('resize', debouncedOptimize);
        
        console.log('Select optimization initialized');
    }

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 暴露一些方法供外部调用
    window.SelectOptimization = {
        refresh: optimizeDropdownOptions,
        isMobile: isMobileDevice
    };

})();
